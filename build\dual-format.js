const path = require("path");
const webpack = require("webpack");
const StaticConfig = require("./static");
const externals = require("./externals");

// 基础配置
const baseConfig = {
  mode: "production",
  entry: {
    index: path.resolve(__dirname, "../src/index.ts"),
  },
  devtool: "source-map",
  resolve: {
    extensions: [".tsx", ".ts", ".jsx", ".js", ".json"],
    alias: {
      "@components": path.resolve(__dirname, "../src/components"),
      "@utils": path.resolve(__dirname, "../src/utils"),
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /(node_modules|bower_components)/,
        use: [
          {
            loader: require.resolve("babel-loader"),
            options: {
              babelrc: false,
              presets: [
                require.resolve("@babel/preset-react"),
                [
                  require.resolve("@babel/preset-env"),
                  {
                    modules: false, // 保持 ES modules
                    targets: {
                      browsers: ["> 1%", "last 2 versions", "not ie <= 8"],
                    },
                  },
                ],
                require.resolve("@babel/preset-typescript"),
              ],
              plugins: [
                ["@babel/plugin-proposal-class-properties", { loose: true }],
                ["@babel/plugin-proposal-decorators", { legacy: true }],
                [
                  "@babel/plugin-proposal-private-property-in-object",
                  { loose: true },
                ],
                ["@babel/plugin-proposal-private-methods", { loose: true }],
                "@babel/plugin-transform-arrow-functions",
              ],
            },
          },
        ],
      },
      ...StaticConfig.rules(),
    ],
  },
  externals: externals,
  plugins: [
    ...StaticConfig.plugins(),
    new webpack.LoaderOptionsPlugin({
      debug: false,
    }),
  ],
};

// ES Modules 配置 - 使用更简单的配置
const esmConfig = {
  ...baseConfig,
  output: {
    filename: "[name].esm.js",
    path: path.resolve(__dirname, "../es"),
    library: {
      type: "umd",
      name: "coreUI",
      umdNamedDefine: true,
    },
    globalObject: "typeof self !== 'undefined' ? self : this",
    chunkFormat: "array-push",
  },
  target: ["web", "es2015"],
};

// CommonJS 配置
const cjsConfig = {
  ...baseConfig,
  output: {
    filename: "[name].js",
    path: path.resolve(__dirname, "../lib"),
    library: {
      type: "commonjs2",
    },
    chunkFormat: "commonjs",
  },
  target: "node",
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /(node_modules|bower_components)/,
        use: [
          {
            loader: require.resolve("babel-loader"),
            options: {
              babelrc: false,
              presets: [
                require.resolve("@babel/preset-react"),
                [
                  require.resolve("@babel/preset-env"),
                  {
                    modules: "cjs", // 转换为 CommonJS
                    targets: {
                      node: "12", // 支持 Node.js 12+
                    },
                  },
                ],
                require.resolve("@babel/preset-typescript"),
              ],
              plugins: [
                ["@babel/plugin-proposal-class-properties", { loose: true }],
                ["@babel/plugin-proposal-decorators", { legacy: true }],
                [
                  "@babel/plugin-proposal-private-property-in-object",
                  { loose: true },
                ],
                ["@babel/plugin-proposal-private-methods", { loose: true }],
                "@babel/plugin-transform-arrow-functions",
              ],
            },
          },
        ],
      },
      ...StaticConfig.rules(),
    ],
  },
  externals: externals,
  plugins: [
    ...StaticConfig.plugins(),
    new webpack.LoaderOptionsPlugin({
      debug: false,
    }),
  ],
};

module.exports = [esmConfig, cjsConfig];
