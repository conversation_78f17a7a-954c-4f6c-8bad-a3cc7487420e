import React, { useEffect, useState } from "react";
import "./index.scss";
import { Button, ConfigProvider, Progress, Upload, message, Image } from "antd";
import { CloudUploadOutlined, PaperClipOutlined } from "@ant-design/icons";
import { cloneDeep } from "lodash";
import SparkMD5 from "spark-md5";
import type { UploadProps } from "antd";
import { request } from "../../fetch";
const { Dragger } = Upload;
export interface FileUploadProps {
  /** 接受上传的文件类型  */
  accept: string;
  /** 文件列表类型 */
  fileListType: "picture" | "file";
  /** 获取预签名地址 */
  getPreSignatureUrl: string;

  uploadedFileList?: {
    fileKey: string;
    url: string;
    uid: string;
  }[];
  /** 开始上传回调 */
  onStart?: () => void;
  /** 上传文件结束 */
  onEnd?: (fileKey: string) => void;
  /** 上传进度回调 */
  onProgress?: (event: { percent: number }) => void;
  /** 上传文件改变时的回调 */
  onChange?: (opt: any) => void;
  /** 删除上传文件 */
  onDelete?: (fileKey: string) => void;
  /** 上传失败回调 */
  onError?: (event: Error, body?: Object) => void;
  /** 图片上传按钮宽度 */
  btnWidth?: number;
  /** 图片上传按钮高度 */
  btnHeight?: number;
  /** 上传按钮是否可用 */
  disabled?: boolean;
  /** 上传按钮 */
  uploadBtn?: {
    /** 普通按钮 | 图标按钮 | 拖拽上传 */
    type: "btn" | "icon" | "dragger";
    /**  按钮文字 */
    btnText: string;
    /** 拖拽区域配置（type="dragger" 时使用） */
    draggerConfig?: {
      title?: string;
      hint?: string;
    };
  };
  /** 最大上传数量 */
  maxCount?: number;
  /** 最大文件大小 */
  maxFileSize?: number;
  /** 文件大小单位 */
  unit?: Units;
  /** 文件后缀校验 */
  extensionValidate?: (fileKey: string) => boolean;
  /** md5校验 */
  needMd5Validete?: boolean;
  /** 桶名称 */
  bucketName?: string;
  /** 服务域 */
  LOPDN: string;
}

export type Units = "MB" | "GB" | "KB";
const FileUpload: React.FC<FileUploadProps> = ({
  accept,
  getPreSignatureUrl,
  fileListType,
  onStart = () => {},
  onEnd = (fileKey: string) => {},
  onProgress = (event: { percent: number }) => {},
  onChange = (opt: any) => {},
  onError = (event: Error, body?: Object) => {},
  onDelete = (fileKey: string) => {},
  btnWidth = 80,
  btnHeight = 80,
  disabled = false,
  uploadBtn = {
    type: "icon",
    btnText: "upload",
  },
  maxCount = 5,
  unit = "MB",
  maxFileSize = 50, // 单位M
  extensionValidate,
  needMd5Validete,
  bucketName,
  uploadedFileList,
  LOPDN,
}) => {
  const [btnDisabled, setBtnDisable] = useState<boolean>(false);
  const [fileList, setFileList] = useState<
    {
      fileKey: string;
      url: string;
      uid: string;
      status: "active" | "exception" | "success" | "preview";
      showProgress: boolean;
      originFileObj: File | null;
      percentage: number;
    }[]
  >([]);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState("");
  useEffect(() => {
    let arr = fileList;
    if (Array.isArray(uploadedFileList)) {
      uploadedFileList.forEach((v) => {
        onEnd(v.fileKey);
        arr.unshift({
          ...v,
          status: "preview",
          percentage: 100,
          showProgress: false,
          originFileObj: null,
        });
      });
      setFileList(arr);
    }
  }, [JSON.stringify(uploadedFileList)]);

  const uploadProps: UploadProps = {
    maxCount: 1,
    accept: accept,
    openFileDialogOnClick: !!accept && fileList.length < maxCount,
    showUploadList: false,
    beforeUpload: (file: any) => {
      let flag = true;
      if (extensionValidate) {
        flag = extensionValidate(file.name);
      }
      if (!flag) {
        message.warning("文件格式不对");
        return false;
      }
      const unitMap = {
        KB: Math.pow(1024, 1),
        MB: Math.pow(1024, 2),
        GB: Math.pow(1024, 3),
      };
      const fileSize = file.size / unitMap[unit] < maxFileSize;
      if (!fileSize) {
        message.warning("文件大小不能超过" + maxFileSize + unit);
        return false;
      }
      return fileSize || Upload.LIST_IGNORE;
    },
    customRequest: async (option: any) => {
      const { file, onProgress, onSuccess } = option;
      beforeUploadRequest(file);
      let promiseArr = [getS3InfoAsync(file)];
      if (needMd5Validete) {
        promiseArr.push(getFileMd5Async(file));
      }
      Promise.all(promiseArr)
        .then(async (res) => {
          const [s3Info, md5Info]: any = res;
          if (needMd5Validete && !md5Info) {
            message.error("文件读取失败，请重新上传！");
            return;
          }
          const { uploadUrl, bucketName, fileKey } = s3Info.data;
          const base64Str = await getBase64(file);
          const tempFileList = cloneDeep(fileList);
          const currentFile: any = {
            uid: file.uid,
            url: base64Str,
            originFileObj: file,
            fileKey: fileKey,
            showProgress: true,
            status: "active",
            percentage: 0,
          };
          tempFileList.push(currentFile);
          setFileList(tempFileList);
          const xhr = new XMLHttpRequest();
          xhr.open("PUT", uploadUrl);

          xhr.upload.addEventListener("progress", (e) => {
            if (e.lengthComputable) {
              const percentage = Math.round((e.loaded / e.total) * 100);
              const newTemp = cloneDeep(tempFileList);
              const currentFile = newTemp.find((item) => item.uid === file.uid);
              currentFile!.percentage = percentage;
              onProgress({ percent: percentage });
              setFileList(newTemp);
            }
          });

          xhr.onload = () => {
            if (xhr.status === 200) {
              onSuccess(xhr, file);
              onEnd(fileKey);
              const newTemp = cloneDeep(tempFileList);
              const currentFile = newTemp.find((item) => item.uid === file.uid);
              currentFile!.percentage = 100;
              currentFile!.showProgress = false;
              currentFile!.status = "success";
              setFileList(newTemp);
            } else {
              const newTemp = cloneDeep(tempFileList);
              const currentFile = newTemp.find((item) => item.uid === file.uid);
              currentFile!.percentage = 0;
              currentFile!.showProgress = false;
              currentFile!.status = "exception";
              setFileList(newTemp);
              onError(fileKey);
            }
          };

          xhr.onerror = () => {
            const newTemp = cloneDeep(tempFileList);
            const currentFile = newTemp.find((item) => item.uid === file.uid);
            currentFile!.percentage = 0;
            currentFile!.showProgress = false;
            currentFile!.status = "exception";
            setFileList(newTemp);
            onError(fileKey);
          };

          xhr.setRequestHeader(
            "Content-Type",
            file.type || "application/octet-stream"
          );
          xhr.setRequestHeader("Content-Length", file.size);

          xhr.send(file);
        })
        .catch((e) => {
          message.error(e.message);
          onError(e);
        });
    },
  };

  const getBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });
  };

  const beforeUploadRequest = (file: any) => {
    onStart && onStart(); // 调用开始上传函数(主要目的是将升级包格式设置为不可用)
  };

  const createFileChunk = (file: any, size = 2 * 1024 * 1024) => {
    const fileChunkList: any = [];
    let cur = 0;
    while (cur < file.size) {
      fileChunkList.push({ file: file?.slice(cur, cur + size) });
      cur += size;
    }
    return fileChunkList;
  };

  const getFileMd5Async = (file: any) => {
    let fileHash = null;
    // 这里需要使用异步执行，保证获取到hash后执行下一步
    return new Promise((resolve) => {
      const fileChunks = createFileChunk(file);
      const spark = new SparkMD5.ArrayBuffer();
      let hadReadChunksNum = 0;
      const readFile = (chunkIndex: any) => {
        const fileReader = new FileReader();
        fileReader.readAsArrayBuffer(fileChunks[chunkIndex]?.file);
        fileReader.onload = (e: any) => {
          hadReadChunksNum++;
          spark.append(e.target.result);
          if (hadReadChunksNum === fileChunks.length) {
            fileHash = spark.end();
            fileReader.onload = null;
            resolve(fileHash);
          } else {
            readFile(hadReadChunksNum);
          }
        };
      };
      readFile(0);
    });
  };

  const getS3InfoAsync = (file: any) => {
    return request({
      absoluteURL: getPreSignatureUrl,
      method: "POST",
      headers: {
        "LOP-DN": LOPDN,
      },
      body: {
        fileKey: `${Date.now()}-${file.name}`,
        bucketName: bucketName,
      },
    });
  };

  const handlePreview = async (file: any) => {
    setPreviewImage(file.url!);
    setPreviewOpen(true);
  };

  const handleDelete = (item: {
    uid: string | number;
    fileKey: string;
    url: string;
    originFileObj: File;
  }) => {
    setFileList(fileList.filter((file) => file.uid !== item.uid));
    onDelete(item.fileKey);
  };

  const onUploadChange = (info: any) => {};
  useEffect(() => {
    setBtnDisable(disabled);
  }, [disabled]);

  return (
    <ConfigProvider prefixCls="x-coreui">
      <div className={`upload-list ${fileListType}-container`}>
        {uploadBtn.type === "dragger" ? (
          <Dragger
            {...uploadProps}
            onChange={onUploadChange}
            disabled={fileList.length >= maxCount}
            style={{ marginTop: "16px" }}
          >
            <div>
              <CloudUploadOutlined
                style={{ fontSize: "32px", color: "#3c6ef0" }}
              />
            </div>
            <div className="dragger-title">
              {uploadBtn.draggerConfig?.title || uploadBtn.btnText}
            </div>
            <div className="dragger-hint">
              {uploadBtn.draggerConfig?.hint || ""}
            </div>
          </Dragger>
        ) : (
          <Upload {...uploadProps} onChange={onUploadChange}>
            {uploadBtn.type === "icon" ? (
              fileList.length >= maxCount ? null : (
                <span
                  className="upload-btn"
                  style={{
                    width: "80px",
                    height: "80px",
                  }}
                >
                  <svg
                    viewBox="64 64 896 896"
                    focusable="false"
                    data-icon="plus"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"></path>
                    <path d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"></path>
                  </svg>
                  <i>{uploadBtn.btnText}</i>
                </span>
              )
            ) : (
              <Button type="primary" disabled={btnDisabled}>
                {uploadBtn.btnText}
              </Button>
            )}
          </Upload>
        )}
        {fileList?.map((item: any) =>
          fileListType === "picture" ? (
            <div
              className="pic-item"
              style={{
                width: `${btnWidth}px`,
                height: `${btnHeight}px`,
              }}
            >
              <Image
                width="100%"
                height="100%"
                preview={false}
                key={item.uid}
                src={item.url}
              />
              <div
                className="action-btn"
                style={{
                  width: "100%",
                  height: "100%",
                }}
              >
                <svg
                  viewBox="64 64 896 896"
                  focusable="false"
                  data-icon="eye"
                  width="1em"
                  height="1em"
                  fill="#fff"
                  aria-hidden="true"
                  onClick={handlePreview.bind(null, item)}
                >
                  <path d="M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"></path>
                </svg>

                <svg
                  viewBox="64 64 896 896"
                  focusable="false"
                  data-icon="delete"
                  width="1em"
                  height="1em"
                  fill="#fff"
                  aria-hidden="true"
                  style={{ marginLeft: "4px" }}
                  onClick={handleDelete.bind(null, item)}
                >
                  <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
                </svg>
              </div>
            </div>
          ) : (
            <div className="file-preview">
              <div className="file-preview-container">
                <div className="file-name-container">
                  <PaperClipOutlined
                    style={{ color: "#525765", fontSize: "13px" }}
                  />
                  <div className="file-name">{item.fileKey}</div>
                </div>
                <div className="file-action-container">
                  {item.status === "preview" && (
                    <svg
                      onClick={() => {
                        if (item.url) {
                          item?.url?.startsWith("https")
                            ? window.open(item?.url)
                            : window.open(item?.url?.replace("http", "https"));
                        }
                      }}
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="4556"
                      width="14"
                      height="14"
                    >
                      <path
                        d="M1009.430069 759.899433c-1.432926-33.366708-23.131522-51.790044-51.994748-51.175933-28.453819 0.614111-47.286562 19.446855-49.947711 53.632378-2.047037 24.973855-0.204704 50.357118-0.818815 75.535677-1.432926 63.253453-15.148076 77.787418-78.196825 79.015641-61.001712 1.228222-121.79872 0.409407-182.800433 0.409407-140.836168 0-281.467631 0.204704-422.303799 0-97.029569-0.204704-105.422422-8.597557-105.831829-102.761273 0-16.785706 0.818815-33.776116-0.818815-50.357118-3.27526-35.413746-19.856262-54.041785-48.105377-55.474712-30.500856-1.63763-52.608859 19.037447-54.860601 55.474712-2.047037 33.571412-1.023519 67.142824-0.614111 100.918939 0.818815 108.902385 48.924192 157.621874 157.826578 158.235985 113.405867 0.614111 226.811735 0.204704 340.422306 0.204704H612.304829c86.180271 0 172.360542 1.228222 258.336109-0.409408 75.945085-1.432926 131.419796-45.444228 137.765612-111.972941 4.503482-50.152414 3.070556-101.123644 1.023519-151.276058z"
                        p-id="4557"
                        fill="#cdcdcd"
                      ></path>
                      <path
                        d="M128.58991 425.208832c108.288274 108.288274 216.576548 216.781252 324.864823 325.069526 4.503482 4.503482 9.006964 8.80226 13.305742 13.305742 17.399817 17.604521 37.665487 27.635004 62.434638 18.218633 7.983446-3.070556 13.919854-7.574038 19.037448-12.896336 3.27526-2.251741 6.755223-4.91289 9.621075-7.778741 55.679415-54.246489 292.93104-292.316929 347.79164-347.17753 5.936408-5.936408 11.463409-12.282224 16.171595-19.242151 14.943372-22.108003 12.896335-43.806599-4.912889-63.253453-18.013928-19.856262-40.940746-20.675077-63.253453-9.621075-10.849298 5.322297-19.856262 15.148076-28.658523 23.950337-36.846672 36.641968-170.313505 169.904098-259.769036 259.359628V185.705465c0-41.964265 1.432926-84.133234-0.818815-125.892795-1.842334-38.893709-20.879781-58.749971-51.175933-59.77349-31.114967-1.023519-50.152414 18.013928-54.451192 56.293527-1.63763 14.533965-0.818815 29.272634-0.818815 44.011302v504.594699l-14.329261-14.124557c-27.839708-27.635004-55.474711-55.270008-83.314419-82.905012-57.931156-57.931156-115.043497-116.476423-173.998172-173.179357-26.611485-25.587966-57.521749-25.997374-78.606234-5.936408-22.926818 21.698596-22.108003 47.491266 2.865853 77.992122 5.731704 6.755223 12.07752 12.486928 18.013928 18.423336z"
                        p-id="4558"
                        fill="#cdcdcd"
                      ></path>
                    </svg>
                  )}
                  <svg
                    viewBox="64 64 896 896"
                    focusable="false"
                    data-icon="delete"
                    width="1em"
                    height="1em"
                    fill={
                      item.status && item.status === "exception"
                        ? "rgb(255, 77, 79)"
                        : "rgba(0, 0, 0, 0.45)"
                    }
                    aria-hidden="true"
                    onClick={handleDelete.bind(null, item)}
                  >
                    <path d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"></path>
                  </svg>
                </div>
              </div>
              {item.status != "preview" && (
                <Progress
                  percent={item.percentage && item.percentage}
                  size="small"
                  status={item.status && item.status}
                />
              )}
            </div>
          )
        )}
        {previewImage && (
          <Image
            wrapperStyle={{ display: "none" }}
            preview={{
              visible: previewOpen,
              onVisibleChange: (visible) => setPreviewOpen(visible),
              afterOpenChange: (visible) => !visible && setPreviewImage(""),
            }}
            src={previewImage}
          />
        )}
      </div>
    </ConfigProvider>
  );
};

export default FileUpload;
