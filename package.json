{"name": "@jd/x-coreui", "version": "1.1.78", "description": "", "main": "lib/index.js", "module": "es/index.esm.js", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build": "npm run build:types && webpack --config build/dual-format.js", "build:old": "npm run build:types && webpack --config build/prod.js", "prepublish": "npm version patch", "build:types": "tsc -p tsconfig.json --outDir lib"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.22.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "7.22.7", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@babel/plugin-transform-arrow-functions": "7.22.5", "@babel/plugin-transform-regenerator": "7.22.5", "@babel/plugin-transform-runtime": "7.22.9", "@babel/preset-env": "^7.22.10", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "7.22.5", "@babel/runtime": "7.22.6", "@babel/runtime-corejs2": "7.22.6", "@storybook/addon-essentials": "^7.2.2", "@storybook/addon-interactions": "^7.2.2", "@storybook/addon-links": "^7.2.2", "@storybook/addon-onboarding": "^1.0.8", "@storybook/addon-styling": "^1.3.7", "@storybook/blocks": "^7.2.2", "@storybook/preset-scss": "^1.0.3", "@storybook/react": "^7.2.2", "@storybook/react-webpack5": "^7.2.2", "@storybook/testing-library": "^0.2.0", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@types/spark-md5": "^3.0.5", "babel-loader": "^9.1.3", "babel-preset-react-app": "10.0.1", "css-loader": "6.7.2", "less": "4.1.3", "less-loader": "11.1.0", "mini-css-extract-plugin": "2.7.0", "postcss-loader": "7.0.1", "prop-types": "^15.8.1", "sass": "^1.69.5", "sass-loader": "^10.5.1", "storybook": "^7.2.2", "style-loader": "^3.3.1", "ts-loader": "9.4.4", "typescript": "5.1.6", "webpack": "5.88.2", "webpack-cli": "5.1.4", "webpack-merge": "^5.9.0"}, "dependencies": {"antd": "^5.24.2", "classnames": "^2.3.2", "dayjs": "^1.11.10", "jquery": "^3.7.1", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-resizable": "^3.0.5", "spark-md5": "^3.0.2", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "files": ["lib", "es"]}